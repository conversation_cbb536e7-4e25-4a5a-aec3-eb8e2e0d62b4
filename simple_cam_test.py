#!/usr/bin/env python3
"""
Simple CAM test script for DynamicTemporalAttention
"""

import os
import torch
import numpy as np
import cv2
import glob
import argparse
from collections import OrderedDict

# Import our modules
from slr_network import SLRModel
from utils.device import GpuDataParallel
from generate_cam_dta import DynamicTemporalAttentionCAM


def create_simple_model():
    """Create a simple model for testing"""
    print("Creating simple model for testing...")

    # Create a dummy gloss_dict with enough entries to avoid KeyError
    dummy_gloss_dict = {f'word_{i}': [i] for i in range(1000)}

    try:
        model = SLRModel(
            num_classes=1000,
            c2d_type='resnet18',
            conv_type=2,
            use_bn=1,
            gloss_dict=dummy_gloss_dict
        )
        print("✓ Model created successfully")

        # Set model to training mode to avoid decoder calls
        model.train()

        return model
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_model_forward():
    """Test model forward pass"""
    print("\nTesting model forward pass...")
    
    model = create_simple_model()
    if model is None:
        return False
    
    # Keep model in training mode to avoid decoder calls
    model.train()

    try:
        # Create dummy input - SLRModel expects (B, T, C, H, W) format
        batch_size = 1
        temporal = 16
        channels = 3
        height = 224
        width = 224

        dummy_input = torch.randn(batch_size, temporal, channels, height, width)
        vid_lgt = torch.LongTensor([temporal])

        print(f"✓ Created dummy input: {dummy_input.shape}")

        # Forward pass
        with torch.no_grad():
            output = model(dummy_input, vid_lgt)
        
        print("✓ Forward pass successful")
        print(f"✓ Output keys: {list(output.keys())}")
        
        if 'sequence_logits' in output:
            seq_logits = output['sequence_logits']
            print(f"✓ Sequence logits shape: {seq_logits.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cam_generation():
    """Test CAM generation"""
    print("\nTesting CAM generation...")
    
    model = create_simple_model()
    if model is None:
        return False
    
    # Keep model in training mode to avoid decoder calls during CAM generation
    model.train()

    try:
        # Initialize CAM
        target_layers = ['conv2d.corr2.fusion.0', 'conv2d.corr1.fusion.0']
        dta_cam = DynamicTemporalAttentionCAM(model, target_layers=target_layers)
        
        print("✓ DynamicTemporalAttentionCAM initialized")
        print(f"✓ Target layers: {dta_cam.target_layers}")
        print(f"✓ Hooks registered: {len(dta_cam.hooks)}")
        
        # Create dummy input - SLRModel expects (B, T, C, H, W) format
        dummy_input = torch.randn(1, 16, 3, 224, 224)
        vid_lgt = torch.LongTensor([16])
        
        # Generate CAM
        for layer_name in target_layers:
            print(f"\nTesting layer: {layer_name}")
            
            cam, attention_info = dta_cam.generate_cam(
                dummy_input, vid_lgt=vid_lgt, layer_name=layer_name
            )
            
            if cam is not None:
                print(f"✓ CAM generated for {layer_name}")
                print(f"✓ CAM shape: {cam.shape}")
                
                if attention_info:
                    print(f"✓ Attention info keys: {list(attention_info.keys())}")
                else:
                    print("- No attention info")
            else:
                print(f"✗ CAM generation failed for {layer_name}")
        
        # Clean up
        dta_cam.remove_hooks()
        print("✓ Hooks removed")
        
        return True
        
    except Exception as e:
        print(f"✗ CAM generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_with_real_data():
    """Test with real data if available"""
    print("\nTesting with real data...")
    
    # Check if data files exist
    dict_path = "./preprocess/phoenix2014/gloss_dict.npy"
    dev_info_path = "./preprocess/phoenix2014/dev_info.npy"
    
    if not os.path.exists(dict_path) or not os.path.exists(dev_info_path):
        print("⚠ Real data files not found, skipping real data test")
        return True
    
    try:
        # Load real data
        gloss_dict = np.load(dict_path, allow_pickle=True).item()
        inputs_list = np.load(dev_info_path, allow_pickle=True).item()
        
        print(f"✓ Loaded gloss_dict with {len(gloss_dict)} entries")
        print(f"✓ Loaded {len(inputs_list)} video entries")
        
        # Create model with real gloss_dict
        model = SLRModel(
            num_classes=1000,
            c2d_type='resnet18',
            conv_type=2,
            use_bn=1,
            gloss_dict=gloss_dict
        )
        print("✓ Model created with real gloss_dict")
        
        # Test with first video info
        video_info = inputs_list[0]
        print(f"✓ Testing with video: {video_info['fileid']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Real data test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    parser = argparse.ArgumentParser(description='Simple CAM test for DynamicTemporalAttention')
    parser.add_argument('--skip_real_data', action='store_true',
                        help='Skip real data test')
    
    args = parser.parse_args()
    
    print("=== Simple CAM Test for DynamicTemporalAttention ===\n")
    
    tests = [
        ("Model Creation", lambda: create_simple_model() is not None),
        ("Model Forward Pass", test_model_forward),
        ("CAM Generation", test_cam_generation),
    ]
    
    if not args.skip_real_data:
        tests.append(("Real Data Test", test_with_real_data))
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"Running: {test_name}")
        print("-" * 50)
        
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results[test_name] = False
        
        print()
    
    # Summary
    print("=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status:<8} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! DynamicTemporalAttention CAM is working.")
        print("\nYou can now try:")
        print("  python generate_cam_dta.py --help")
        print("  python generate_cam_dta.py --select_id 0")
    else:
        print(f"\n⚠ {total-passed} tests failed. Check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
