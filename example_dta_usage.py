#!/usr/bin/env python3
"""
Example usage of DynamicTemporalAttention CAM generation tools
"""

import os
import subprocess
import sys

def run_command(cmd, description, check_output=True):
    """Run a command and print results"""
    print(f"\n{'='*70}")
    print(f"EXAMPLE: {description}")
    print('='*70)
    print(f"Command: {' '.join(cmd)}")
    print()
    
    try:
        if check_output:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("✓ Success!")
            if result.stdout:
                print("Output:")
                print(result.stdout[-500:])  # Show last 500 chars
        else:
            # For interactive commands, just show the command
            print("Run this command manually:")
            print(f"  {' '.join(cmd)}")
            return True
    except subprocess.CalledProcessError as e:
        print("✗ Failed!")
        print(f"Error: {e.stderr}")
        return False
    except FileNotFoundError:
        print("✗ Script not found! Make sure you're in the correct directory.")
        return False
    
    return True

def main():
    print("=== DynamicTemporalAttention CAM Generation Examples ===")
    print("This script demonstrates the DTA-specific CAM generation tools.")
    print("Make sure you have set up your model checkpoint and data paths first!")
    
    # Example 1: Test DTA setup
    print("\n" + "="*70)
    print("STEP 1: Testing DynamicTemporalAttention Setup")
    print("="*70)
    
    cmd = [sys.executable, 'test_dta_cam.py']
    if not run_command(cmd, "Test DTA CAM setup and architecture"):
        print("DTA setup test failed. Please fix the issues before proceeding.")
        return
    
    # Ask user if they want to continue
    print("\n" + "="*70)
    print("READY FOR DTA CAM GENERATION")
    print("="*70)
    
    response = input("Do you want to proceed with DTA CAM generation examples? (y/n): ").lower()
    if response != 'y':
        print("Stopping here. You can run the examples manually when ready.")
        return
    
    # Example 2: Single video DTA CAM with attention visualization
    cmd = [
        sys.executable, 'generate_cam_dta.py',
        '--select_id', '0',
        '--target_layers', 'conv2d.corr2.fusion.0', 'conv2d.corr2.temporal_gate.0',
        '--alpha', '0.4',
        '--save_original',
        '--visualize_attention',
        '--output_dir', './example_DTA_CAM'
    ]
    run_command(cmd, "Generate DTA CAM with attention visualization", check_output=False)
    
    # Example 3: Multi-layer DTA analysis
    cmd = [
        sys.executable, 'generate_cam_dta.py',
        '--select_id', '1',
        '--target_layers', 
        'conv2d.corr2.fusion.0', 
        'conv2d.corr1.fusion.0',
        'conv2d.corr2.temporal_gate.0',
        'conv2d.layer4.1.conv2',
        '--alpha', '0.5',
        '--save_original',
        '--visualize_attention',
        '--output_dir', './example_DTA_multilayer'
    ]
    run_command(cmd, "Multi-layer DTA CAM analysis", check_output=False)
    
    # Example 4: DTA batch processing with experiment tracking
    cmd = [
        sys.executable, 'batch_generate_cam_dta.py',
        '--start_id', '0',
        '--end_id', '3',
        '--target_layers', 'conv2d.corr2.fusion.0', 'conv2d.corr1.fusion.0',
        '--experiment_name', 'dta_fusion_comparison',
        '--visualize_attention',
        '--save_original',
        '--output_dir', './example_DTA_batch'
    ]
    run_command(cmd, "DTA batch processing with experiment tracking", check_output=False)
    
    # Example 5: Temporal gate analysis
    cmd = [
        sys.executable, 'batch_generate_cam_dta.py',
        '--start_id', '0',
        '--end_id', '2',
        '--target_layers', 'conv2d.corr2.temporal_gate.0', 'conv2d.corr1.temporal_gate.0',
        '--experiment_name', 'temporal_gate_analysis',
        '--visualize_attention',
        '--alpha', '0.6',
        '--output_dir', './example_DTA_gates'
    ]
    run_command(cmd, "Temporal gate analysis", check_output=False)
    
    # Example 6: Configuration-based DTA experiments
    if os.path.exists('cam_config.yaml'):
        cmd = [
            sys.executable, 'advanced_cam_generator.py',
            '--config', 'cam_config.yaml',
            '--experiment', 'dta_fusion_only',
            '--video_ids', '0', '1', '2',
            '--create_comparisons'
        ]
        run_command(cmd, "Configuration-based DTA experiments", check_output=False)
    else:
        print("\nSkipping config-based example (cam_config.yaml not found)")
    
    # Example 7: Create DTA-specific comparisons
    print(f"\n{'='*70}")
    print("EXAMPLE: Create DTA-specific comparison visualizations")
    print('='*70)
    
    comparison_commands = [
        # Layer comparison for DTA
        [
            sys.executable, 'visualize_cam_comparison.py',
            '--input_dir', './example_DTA_multilayer',
            '--output_dir', './example_DTA_comparisons',
            '--comparison_type', 'layers',
            '--max_frames', '6'
        ],
        # Video comparison for fusion layers
        [
            sys.executable, 'visualize_cam_comparison.py',
            '--input_dir', './example_DTA_batch',
            '--output_dir', './example_DTA_comparisons',
            '--comparison_type', 'videos',
            '--layer_name', 'conv2d_corr2_fusion_0',
            '--max_frames', '6'
        ]
    ]
    
    for cmd in comparison_commands:
        run_command(cmd, f"DTA comparison: {cmd[-3]}", check_output=False)
    
    print("\n" + "="*70)
    print("DTA EXAMPLES COMPLETED")
    print("="*70)
    
    print("\nGenerated outputs (check if they exist):")
    output_dirs = [
        './example_DTA_CAM',
        './example_DTA_multilayer', 
        './example_DTA_batch',
        './example_DTA_gates',
        './example_DTA_comparisons'
    ]
    
    for output_dir in output_dirs:
        if os.path.exists(output_dir):
            print(f"✓ {output_dir}")
        else:
            print(f"- {output_dir} (not created - run commands manually)")
    
    print("\nDynamicTemporalAttention CAM Features:")
    print("1. ✓ Multi-scale temporal attention visualization")
    print("2. ✓ Temporal gate activation maps")
    print("3. ✓ Feature fusion layer analysis")
    print("4. ✓ Attention weight plots and summaries")
    print("5. ✓ Experiment tracking and organization")
    
    print("\nNext steps:")
    print("1. Explore the generated DTA CAM visualizations")
    print("2. Analyze attention weight plots in attention_weights/ subdirectories")
    print("3. Compare fusion vs. temporal gate activations")
    print("4. Use different kernel_sizes in attention_params for experiments")
    
    print("\nAdvanced DTA analysis:")
    print("  # Analyze window size effects")
    print("  python generate_cam_dta.py --attention_config custom_attention.yaml")
    print("  ")
    print("  # Compare different reduction ratios")
    print("  python batch_generate_cam_dta.py --experiment_name reduction_ratio_study")
    
    print("\nFor more DTA-specific options, run:")
    print("  python generate_cam_dta.py --help")
    print("  python batch_generate_cam_dta.py --help")
    print("  python test_dta_cam.py")

if __name__ == "__main__":
    main()
