#!/usr/bin/env python3
"""
Batch Grad-CAM generation for DynamicTemporalAttention architecture
"""

import os
import argparse
import subprocess
import numpy as np
from tqdm import tqdm
import yaml


def load_attention_config(config_path):
    """Load attention configuration if provided"""
    if config_path and os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
            return config.get('attention_params', None)
    return None


def main():
    parser = argparse.ArgumentParser(description='Batch generate Grad-CAM for DynamicTemporalAttention')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to model checkpoint')
    parser.add_argument('--dataset', type=str, default='phoenix2014',
                        help='Dataset name')
    parser.add_argument('--prefix', type=str, required=True,
                        help='Dataset prefix path')
    parser.add_argument('--dict_path', type=str, required=True,
                        help='Path to gloss dictionary')
    parser.add_argument('--output_dir', type=str, default='./batch_DTA_CAM_results',
                        help='Output directory for all CAM results')
    parser.add_argument('--start_id', type=int, default=0,
                        help='Starting video ID')
    parser.add_argument('--end_id', type=int, default=None,
                        help='Ending video ID (if None, process all)')
    parser.add_argument('--gpu_id', type=int, default=0,
                        help='GPU ID to use')
    parser.add_argument('--target_layers', type=str, nargs='+',
                        default=['conv2d.corr2.fusion.0', 'conv2d.corr1.fusion.0'],
                        help='Target layers for CAM generation')
    parser.add_argument('--alpha', type=float, default=0.4,
                        help='Alpha blending factor for CAM overlay')
    parser.add_argument('--save_original', action='store_true',
                        help='Save original frames alongside CAM')
    parser.add_argument('--visualize_attention', action='store_true',
                        help='Generate attention weight visualizations')
    parser.add_argument('--attention_config', type=str, default=None,
                        help='Path to attention configuration file')
    parser.add_argument('--experiment_name', type=str, default='default',
                        help='Experiment name for organization')
    
    args = parser.parse_args()
    
    # Load video list to determine range
    inputs_list = np.load(f"./preprocess/{args.dataset}/dev_info.npy", allow_pickle=True).item()
    total_videos = len(inputs_list)
    
    if args.end_id is None:
        args.end_id = total_videos - 1
    
    args.end_id = min(args.end_id, total_videos - 1)
    
    print(f"Processing videos {args.start_id} to {args.end_id} (total: {args.end_id - args.start_id + 1})")
    print(f"Target layers: {args.target_layers}")
    
    # Create output directory
    experiment_output_dir = os.path.join(args.output_dir, args.experiment_name)
    os.makedirs(experiment_output_dir, exist_ok=True)
    
    # Save experiment configuration
    config_info = {
        'experiment_name': args.experiment_name,
        'target_layers': args.target_layers,
        'video_range': [args.start_id, args.end_id],
        'attention_config': args.attention_config,
        'visualize_attention': args.visualize_attention,
        'alpha': args.alpha
    }
    
    with open(os.path.join(experiment_output_dir, 'experiment_config.yaml'), 'w') as f:
        yaml.dump(config_info, f, default_flow_style=False)
    
    # Process each video
    failed_videos = []
    successful_videos = []
    
    for video_id in tqdm(range(args.start_id, args.end_id + 1), desc="Processing videos"):
        try:
            # Prepare command
            cmd = [
                'python', 'generate_cam_dta.py',
                '--checkpoint', args.checkpoint,
                '--dataset', args.dataset,
                '--prefix', args.prefix,
                '--dict_path', args.dict_path,
                '--select_id', str(video_id),
                '--gpu_id', str(args.gpu_id),
                '--output_dir', os.path.join(experiment_output_dir, f'video_{video_id:04d}'),
                '--target_layers'] + args.target_layers + [
                '--alpha', str(args.alpha)
            ]
            
            if args.save_original:
                cmd.append('--save_original')
            
            if args.visualize_attention:
                cmd.append('--visualize_attention')
            
            if args.attention_config:
                cmd.extend(['--attention_config', args.attention_config])
            
            # Run command
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
            
            if result.returncode != 0:
                print(f"Failed to process video {video_id}")
                print(f"Error: {result.stderr}")
                failed_videos.append(video_id)
            else:
                successful_videos.append(video_id)
                
        except Exception as e:
            print(f"Exception while processing video {video_id}: {e}")
            failed_videos.append(video_id)
    
    # Generate summary statistics
    print(f"\n{'='*60}")
    print("PROCESSING SUMMARY")
    print('='*60)
    
    total_processed = len(successful_videos) + len(failed_videos)
    success_rate = len(successful_videos) / total_processed * 100 if total_processed > 0 else 0
    
    print(f"Total videos processed: {total_processed}")
    print(f"Successfully processed: {len(successful_videos)} ({success_rate:.1f}%)")
    print(f"Failed: {len(failed_videos)}")
    
    if failed_videos:
        print(f"Failed video IDs: {failed_videos}")
        
        # Save failed video list
        failed_file = os.path.join(experiment_output_dir, 'failed_videos.txt')
        with open(failed_file, 'w') as f:
            for vid_id in failed_videos:
                f.write(f"{vid_id}\n")
        print(f"Failed video IDs saved to: {failed_file}")
    
    # Save successful video list
    if successful_videos:
        success_file = os.path.join(experiment_output_dir, 'successful_videos.txt')
        with open(success_file, 'w') as f:
            for vid_id in successful_videos:
                f.write(f"{vid_id}\n")
        print(f"Successful video IDs saved to: {success_file}")
    
    # Generate batch summary visualizations
    if successful_videos and len(successful_videos) >= 3:
        print(f"\nGenerating batch summary visualizations...")
        try:
            # Create layer comparison for first few videos
            from visualize_cam_comparison import create_layer_comparison, create_video_comparison
            
            comparison_dir = os.path.join(experiment_output_dir, 'batch_comparisons')
            os.makedirs(comparison_dir, exist_ok=True)
            
            # Layer comparison for first 3 videos
            for i, video_id in enumerate(successful_videos[:3]):
                video_dir = os.path.join(experiment_output_dir, f'video_{video_id:04d}')
                if os.path.exists(video_dir):
                    output_path = os.path.join(comparison_dir, f'video_{video_id:04d}_layer_comparison.png')
                    create_layer_comparison(video_dir, output_path, max_frames=6)
            
            # Video comparison for first layer
            if args.target_layers:
                layer_name = args.target_layers[0].replace('.', '_')
                video_dirs = [os.path.join(experiment_output_dir, f'video_{vid_id:04d}') 
                             for vid_id in successful_videos[:8]]  # First 8 videos
                video_dirs = [d for d in video_dirs if os.path.exists(d)]
                
                if video_dirs:
                    output_path = os.path.join(comparison_dir, f'batch_video_comparison_{layer_name}.png')
                    create_video_comparison(video_dirs, output_path, layer_name, max_frames=6)
            
            print(f"Batch comparisons saved to: {comparison_dir}")
            
        except Exception as e:
            print(f"Failed to generate batch comparisons: {e}")
    
    print(f"\nBatch processing completed!")
    print(f"Results saved to: {experiment_output_dir}")
    
    if successful_videos:
        print(f"\nNext steps:")
        print(f"1. Explore individual results in {experiment_output_dir}")
        print(f"2. Check batch comparisons in {experiment_output_dir}/batch_comparisons")
        print(f"3. Analyze attention weights if --visualize_attention was used")
        
        # Suggest analysis commands
        print(f"\nSuggested analysis commands:")
        print(f"# Create comprehensive comparisons")
        print(f"python visualize_cam_comparison.py --input_dir {experiment_output_dir} --comparison_type layers")
        print(f"python visualize_cam_comparison.py --input_dir {experiment_output_dir} --comparison_type videos")


if __name__ == "__main__":
    main()
