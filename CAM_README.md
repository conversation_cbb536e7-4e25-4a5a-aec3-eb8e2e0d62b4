# Grad-CAM Visualization for Sign Language Recognition with DynamicTemporalAttention

This directory contains tools for generating and visualizing Grad-CAM (Gradient-weighted Class Activation Mapping) for the sign language recognition model with **DynamicTemporalAttention** architecture.

## Overview

The Grad-CAM implementation provides insights into which spatial and temporal regions the model focuses on when making predictions. This is particularly useful for understanding the **DynamicTemporalAttention** mechanisms, which include:

- **Multi-scale temporal convolutions** with adaptive window sizes
- **Temporal gating mechanisms** for selective attention
- **Feature fusion layers** that combine multi-scale information
- **Dynamic window prediction** for adaptive temporal modeling

## Files Description

### Core Scripts

1. **`generate_cam_dta.py`** - **NEW** DynamicTemporalAttention Grad-CAM script
   - Specialized for DynamicTemporalAttention architecture
   - Captures attention weights and temporal gates
   - Visualizes multi-scale temporal features
   - Generates attention weight plots

2. **`generate_cam.py`** - Original Grad-CAM generation script
   - General-purpose GradCAM implementation
   - Supports multiple target layers
   - Compatible with various architectures

3. **`batch_generate_cam_dta.py`** - **NEW** Batch processing for DTA
   - Process multiple videos with DynamicTemporalAttention
   - Experiment organization and tracking
   - Automatic summary generation

4. **`batch_generate_cam.py`** - Original batch processing script
   - Process multiple videos automatically
   - Error handling and progress tracking

5. **`test_dta_cam.py`** - **NEW** DTA-specific testing script
   - Verify DynamicTemporalAttention setup
   - Test model architecture and target layers
   - Validate CAM generation pipeline

6. **`advanced_cam_generator.py`** - Configuration-based generator
   - Uses YAML configuration files
   - Supports multiple experiments
   - Automatic comparison generation

7. **`visualize_cam_comparison.py`** - Comparison visualization tool
   - Compare different layers for the same video
   - Compare the same layer across different videos
   - Generate publication-ready figures

### Configuration

5. **`cam_config.yaml`** - Configuration file
   - Model and dataset settings
   - CAM generation parameters
   - Experimental configurations
   - Visualization settings

## Quick Start

### 0. Test DynamicTemporalAttention Setup

First, verify your DTA setup:

```bash
python test_dta_cam.py
```

### 1. DynamicTemporalAttention CAM Generation (Recommended)

Generate CAM for DynamicTemporalAttention architecture:

```bash
python generate_cam_dta.py \
    --checkpoint ./work_dir/baseline_res18_attention/_best_model.pt \
    --dataset phoenix2014 \
    --prefix ./dataset/phoenix2014/phoenix-2014-multisigner \
    --dict_path ./preprocess/phoenix2014/gloss_dict.npy \
    --select_id 0 \
    --output_dir ./DTA_CAM_results \
    --target_layers conv2d.corr2.fusion.0 conv2d.corr2.temporal_gate.0 \
    --alpha 0.4 \
    --save_original \
    --visualize_attention
```

### 2. Basic CAM Generation (Legacy)

Generate CAM for general architectures:

```bash
python generate_cam.py \
    --checkpoint ./work_dir/baseline_res18_attention/_best_model.pt \
    --dataset phoenix2014 \
    --prefix ./dataset/phoenix2014/phoenix-2014-multisigner \
    --dict_path ./preprocess/phoenix2014/gloss_dict.npy \
    --select_id 0 \
    --output_dir ./CAM_results \
    --target_layers conv2d.corr2.fusion.0 conv2d.corr1.fusion.0 \
    --alpha 0.4 \
    --save_original
```

### 3. DTA Batch Processing (Recommended)

Process multiple videos with DynamicTemporalAttention:

```bash
python batch_generate_cam_dta.py \
    --checkpoint ./work_dir/baseline_res18_attention/_best_model.pt \
    --dataset phoenix2014 \
    --prefix ./dataset/phoenix2014/phoenix-2014-multisigner \
    --dict_path ./preprocess/phoenix2014/gloss_dict.npy \
    --start_id 0 \
    --end_id 10 \
    --output_dir ./batch_DTA_CAM_results \
    --target_layers conv2d.corr2.fusion.0 conv2d.corr1.fusion.0 \
    --experiment_name dta_fusion_experiment \
    --visualize_attention \
    --save_original
```

### 4. Legacy Batch Processing

Process multiple videos with general architecture:

```bash
python batch_generate_cam.py \
    --checkpoint ./work_dir/baseline_res18_attention/_best_model.pt \
    --dataset phoenix2014 \
    --prefix ./dataset/phoenix2014/phoenix-2014-multisigner \
    --dict_path ./preprocess/phoenix2014/gloss_dict.npy \
    --start_id 0 \
    --end_id 10 \
    --output_dir ./batch_CAM_results \
    --target_layers conv2d.corr2.fusion.0 \
    --save_original
```

### 3. Advanced Configuration-Based Generation

Use the configuration file for complex experiments:

```bash
# Run all experiments defined in config
python advanced_cam_generator.py --config cam_config.yaml --create_comparisons

# Run specific experiment
python advanced_cam_generator.py \
    --config cam_config.yaml \
    --experiment correlation_only \
    --video_ids 0 1 2 3 4 \
    --create_comparisons
```

### 4. Create Comparison Visualizations

Generate comparison plots:

```bash
# Compare layers for each video
python visualize_cam_comparison.py \
    --input_dir ./CAM_results \
    --output_dir ./comparisons \
    --comparison_type layers \
    --max_frames 8

# Compare videos for specific layer
python visualize_cam_comparison.py \
    --input_dir ./CAM_results \
    --output_dir ./comparisons \
    --comparison_type videos \
    --layer_name conv2d_corr2_conv_back \
    --video_ids 0 1 2 3 4
```

## Target Layers for DynamicTemporalAttention

The DynamicTemporalAttention model supports CAM generation for various layers:

### DynamicTemporalAttention Layers (Primary)
- `conv2d.corr2.fusion.0` - **Main DTA fusion layer** (highly recommended)
- `conv2d.corr2.temporal_gate.0` - **Temporal gate activations** (shows temporal attention)
- `conv2d.corr1.fusion.0` - Earlier DTA fusion layer
- `conv2d.corr1.temporal_gate.0` - Earlier temporal gate
- `conv2d.corr3.fusion.0` - Later DTA fusion layer (if available)
- `conv2d.corr3.temporal_gate.0` - Later temporal gate (if available)

### DTA Internal Layers (Advanced)
- `conv2d.corr2.window_convs.0` - First scale temporal convolution
- `conv2d.corr2.window_convs.1` - Second scale temporal convolution
- `conv2d.corr2.window_convs.2` - Third scale temporal convolution

### ResNet Backbone Layers
- `conv2d.layer4.1.conv2` - High-level spatial features
- `conv2d.layer3.1.conv2` - Mid-level spatial features
- `conv2d.layer2.1.conv2` - Low-level spatial features

### Temporal Convolution Layers
- `conv1d.temporal_conv.branches.0.0` - Temporal sequence features

## Output Structure

```
CAM_results/
├── video_0000/
│   ├── conv2d_corr2_conv_back/
│   │   └── video_name/
│   │       ├── frame_0000_cam.jpg      # CAM overlay
│   │       ├── frame_0000_heatmap.jpg  # Heatmap only
│   │       ├── frame_0000_original.jpg # Original frame
│   │       └── ...
│   └── conv2d_corr1_conv_back/
│       └── ...
├── video_0001/
└── ...
```

## Configuration File

The `cam_config.yaml` file allows you to:

- Define multiple experimental setups
- Set different layer combinations
- Configure visualization parameters
- Manage batch processing settings
- Control output formats

Key sections:
- `model`: Model checkpoint and architecture settings
- `dataset`: Data paths and preprocessing
- `cam`: CAM generation parameters
- `experiments`: Different layer combinations to test
- `visualization`: Comparison and plotting settings

## Tips and Best Practices

1. **Layer Selection**: Start with `conv2d.corr2.conv_back` as it captures the main attention mechanism

2. **Memory Management**: Process videos one at a time for large datasets to avoid memory issues

3. **Visualization**: Use alpha=0.4 for good overlay visibility, adjust based on your needs

4. **Comparison**: Generate layer comparisons to understand different attention patterns

5. **Batch Processing**: Use the batch script for processing many videos efficiently

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or process fewer videos at once
2. **Layer Not Found**: Check layer names using `model.named_modules()`
3. **No Gradients**: Ensure the model is in eval mode but gradients are enabled
4. **Empty CAM**: Check if the target layer produces meaningful activations

### Debug Mode

Enable debug mode in the configuration:

```yaml
debug:
  save_intermediate_features: true
  save_gradients: true
  verbose_hooks: true
```

## Requirements

- PyTorch >= 1.8.0
- OpenCV >= 4.5.0
- Matplotlib >= 3.4.0
- NumPy >= 1.20.0
- PyYAML >= 6.0
- tqdm >= 4.62.0

## Citation

If you use this CAM implementation in your research, please cite the original Grad-CAM paper:

```bibtex
@inproceedings{selvaraju2017grad,
  title={Grad-cam: Visual explanations from deep networks via gradient-based localization},
  author={Selvaraju, Ramprasaath R and Cogswell, Michael and Das, Abhishek and Vedantam, Ramakrishna and Parikh, Devi and Batra, Dhruv},
  booktitle={Proceedings of the IEEE international conference on computer vision},
  pages={618--626},
  year={2017}
}
```
