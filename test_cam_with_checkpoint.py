#!/usr/bin/env python3
"""
Test CAM generation with real checkpoint but dummy data
"""

import os
import torch
import numpy as np
import cv2
import argparse
from collections import OrderedDict

# Import our modules
from slr_network import SLRModel
from utils.device import GpuDataParallel
from generate_cam_dta import DynamicTemporalAttentionCAM


def load_model_from_checkpoint(checkpoint_path):
    """Load model with configuration inferred from checkpoint"""
    print(f"Loading model from {checkpoint_path}")
    
    # 设置设备
    device = GpuDataParallel()
    device.set_device(0)
    
    # 加载数据和字典
    dict_path = "./preprocess/phoenix2014/gloss_dict.npy"
    if os.path.exists(dict_path):
        gloss_dict = np.load(dict_path, allow_pickle=True).item()
        print(f"✓ Loaded real gloss_dict with {len(gloss_dict)} entries")
    else:
        # 创建虚拟字典
        gloss_dict = {f'word_{i}': [i] for i in range(1300)}
        print("✓ Created dummy gloss_dict")
    
    # 从检查点推断正确的模型配置
    print("Analyzing checkpoint to determine model configuration...")
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    # 从权重推断配置
    num_classes = 1000  # 默认值
    attention_params = None
    
    # 检查分类器权重来确定类别数
    for key in state_dict.keys():
        if 'classifier.weight' in key:
            num_classes = state_dict[key].shape[1]
            print(f"✓ Detected num_classes from checkpoint: {num_classes}")
            break
    
    # 检查注意力模块权重来确定注意力参数
    max_window_size = 9  # 默认值
    for key in state_dict.keys():
        if 'corr1.window_predictor.2.weight' in key:
            max_window_size = state_dict[key].shape[0]
            print(f"✓ Detected max_window_size from checkpoint: {max_window_size}")
            break
    
    # 检查卷积核尺寸
    kernel_sizes = []
    for i in range(3):  # 通常有3个不同尺寸的卷积核
        key = f'conv2d.corr1.window_convs.{i}.weight'
        if key in state_dict:
            kernel_size = state_dict[key].shape[2]
            kernel_sizes.append(kernel_size)
    
    if kernel_sizes:
        print(f"✓ Detected kernel_sizes from checkpoint: {kernel_sizes}")
        attention_params = {
            'max_window_size': max_window_size,
            'kernel_sizes': kernel_sizes,
            'reduction_ratio': 16
        }
    
    # 创建模型
    print(f"Creating SLRModel with num_classes={num_classes}, attention_params={attention_params is not None}")
    model = SLRModel(
        num_classes=num_classes, 
        c2d_type='resnet18', 
        conv_type=2, 
        use_bn=1,
        gloss_dict=gloss_dict,
        attention_params=attention_params
    )
    print("✓ Model created successfully")
    
    # 加载模型权重
    print("Loading model weights...")
    state_dict = OrderedDict([(k.replace('.module', ''), v) for k, v in state_dict.items()])
    
    try:
        model.load_state_dict(state_dict, strict=True)
        print("✓ Model weights loaded successfully (strict=True)")
    except Exception as e:
        print(f"Failed to load with strict=True: {e}")
        model.load_state_dict(state_dict, strict=False)
        print("✓ Model weights loaded successfully (strict=False)")
    
    model = model.to(device.output_device)
    model.eval()
    
    return model, device, attention_params


def create_dummy_video_data(batch_size=1, temporal=32, channels=3, height=224, width=224):
    """Create dummy video data for testing"""
    print(f"Creating dummy video data: ({batch_size}, {temporal}, {channels}, {height}, {width})")
    
    # Create dummy input - SLRModel expects (B, T, C, H, W) format
    dummy_input = torch.randn(batch_size, temporal, channels, height, width)
    vid_lgt = torch.LongTensor([temporal])
    
    return dummy_input, vid_lgt


def test_cam_generation_with_checkpoint(checkpoint_path):
    """Test CAM generation with real checkpoint"""
    print("=== Testing CAM Generation with Real Checkpoint ===\n")
    
    try:
        # Load model
        model, device, attention_params = load_model_from_checkpoint(checkpoint_path)
        
        # Create dummy data
        dummy_input, vid_lgt = create_dummy_video_data()
        dummy_input = device.data_to_device(dummy_input)
        vid_lgt = device.data_to_device(vid_lgt)
        
        # Test forward pass
        print("\nTesting forward pass...")
        model.train()  # Use training mode to avoid decoder issues
        
        with torch.no_grad():
            output = model(dummy_input, vid_lgt)
        
        print("✓ Forward pass successful")
        print(f"✓ Output keys: {list(output.keys())}")
        
        if 'sequence_logits' in output:
            seq_logits = output['sequence_logits']
            print(f"✓ Sequence logits shape: {seq_logits.shape}")
        
        # Test CAM generation
        print("\nTesting CAM generation...")
        target_layers = [
            'conv2d.corr2.fusion.0',
            'conv2d.corr1.fusion.0',
            'conv2d.corr2.temporal_gate.0'
        ]
        
        dta_cam = DynamicTemporalAttentionCAM(model, target_layers=target_layers)
        print(f"✓ DynamicTemporalAttentionCAM initialized")
        print(f"✓ Target layers: {dta_cam.target_layers}")
        print(f"✓ Hooks registered: {len(dta_cam.hooks)}")
        
        # Generate CAM for each layer
        for layer_name in target_layers:
            print(f"\nTesting layer: {layer_name}")
            
            try:
                cam, attention_info = dta_cam.generate_cam(
                    dummy_input, vid_lgt=vid_lgt, layer_name=layer_name
                )
                
                if cam is not None:
                    print(f"✓ CAM generated for {layer_name}")
                    print(f"✓ CAM shape: {cam.shape}")
                    
                    if attention_info:
                        print(f"✓ Attention info keys: {list(attention_info.keys())}")
                    else:
                        print("- No attention info")
                else:
                    print(f"✗ CAM generation failed for {layer_name}")
                    
            except Exception as e:
                print(f"✗ Error generating CAM for {layer_name}: {e}")
        
        # Clean up
        dta_cam.remove_hooks()
        print("\n✓ Hooks removed")
        
        print("\n🎉 CAM generation test completed successfully!")
        print(f"\nModel configuration used:")
        print(f"  - num_classes: {model.num_classes}")
        print(f"  - attention_params: {attention_params}")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    parser = argparse.ArgumentParser(description='Test CAM generation with real checkpoint')
    parser.add_argument('--checkpoint', type=str, 
                        default='./work_dir/baseline_res18_attention/_best_model.pt',
                        help='Path to model checkpoint')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.checkpoint):
        print(f"✗ Checkpoint not found: {args.checkpoint}")
        print("Please provide a valid checkpoint path")
        return False
    
    success = test_cam_generation_with_checkpoint(args.checkpoint)
    
    if success:
        print("\n✅ All tests passed! Your DynamicTemporalAttention CAM is working with real checkpoint.")
        print("\nNext steps:")
        print("1. Set up your data paths to use real video data")
        print("2. Run: python generate_cam_dta.py --select_id 0 --help")
        print("3. Try batch processing: python batch_generate_cam_dta.py --help")
    else:
        print("\n❌ Tests failed. Please check the errors above.")
    
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
