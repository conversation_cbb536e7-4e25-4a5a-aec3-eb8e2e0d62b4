#!/usr/bin/env python3
"""
验证动态时空注意力机制是否真的被使用
"""

import torch
import torch.nn as nn
import yaml
import numpy as np
from modules.attention import DynamicTemporalAttention
from slr_network import SLRModel
import sys
import os

def check_attention_in_model(model):
    """检查模型中是否包含动态时空注意力机制"""
    print("=== 检查模型中的注意力机制 ===")
    
    attention_modules = []
    for name, module in model.named_modules():
        if isinstance(module, DynamicTemporalAttention):
            attention_modules.append((name, module))
            print(f"✓ 找到动态时空注意力模块: {name}")
            print(f"  - channels: {module.channels}")
            print(f"  - max_window_size: {module.max_window_size}")
            print(f"  - kernel_sizes: {module.kernel_sizes}")
            print(f"  - reduction_ratio: {module.reduction_ratio}")
            print()
    
    if not attention_modules:
        print("✗ 未找到任何动态时空注意力模块")
        return False
    
    print(f"总共找到 {len(attention_modules)} 个动态时空注意力模块")
    return True

def test_attention_forward(model):
    """测试注意力机制的前向传播"""
    print("=== 测试注意力机制前向传播 ===")
    
    model.eval()
    
    # 创建测试输入 (B, T, C, H, W)
    batch_size = 2
    seq_length = 16
    channels = 3
    height = 224
    width = 224
    
    test_input = torch.randn(batch_size, seq_length, channels, height, width)
    vid_lgt = torch.LongTensor([seq_length, seq_length])
    
    print(f"输入形状: {test_input.shape}")
    
    # 注册钩子来捕获注意力模块的输出
    attention_outputs = {}
    
    def hook_fn(name):
        def hook(module, input, output):
            attention_outputs[name] = output.detach()
        return hook
    
    hooks = []
    for name, module in model.named_modules():
        if isinstance(module, DynamicTemporalAttention):
            hook = module.register_forward_hook(hook_fn(name))
            hooks.append(hook)
    
    try:
        with torch.no_grad():
            output = model(test_input, vid_lgt)
        
        print("✓ 前向传播成功")
        print(f"模型输出键: {list(output.keys())}")
        
        # 检查注意力模块的输出
        if attention_outputs:
            print("\n注意力模块输出:")
            for name, att_output in attention_outputs.items():
                print(f"  {name}: {att_output.shape}")
                # 检查输出是否有变化（不全为零）
                if torch.all(att_output == 0):
                    print(f"    ⚠️  警告: {name} 输出全为零")
                else:
                    print(f"    ✓ {name} 输出正常")
        else:
            print("✗ 未捕获到任何注意力模块输出")
            
    except Exception as e:
        print(f"✗ 前向传播失败: {e}")
        return False
    finally:
        # 清理钩子
        for hook in hooks:
            hook.remove()
    
    return True

def load_config_and_test():
    """加载配置并测试"""
    print("=== 加载配置文件 ===")
    
    config_path = "configs/baseline.yaml"
    if not os.path.exists(config_path):
        print(f"✗ 配置文件不存在: {config_path}")
        return False
    
    with open(config_path, 'r') as f:
        config = yaml.load(f, Loader=yaml.FullLoader)
    
    attention_params = config.get('attention_params', None)
    if attention_params:
        print("✓ 配置文件中找到attention_params:")
        for key, value in attention_params.items():
            print(f"  {key}: {value}")
    else:
        print("✗ 配置文件中未找到attention_params")
        return False
    
    # 加载词汇表
    dict_path = './preprocess/phoenix2014-T/gloss_dict.npy'
    if not os.path.exists(dict_path):
        print(f"✗ 词汇表文件不存在: {dict_path}")
        return False
    
    gloss_dict = np.load(dict_path, allow_pickle=True).item()
    num_classes = len(gloss_dict) + 1
    
    print(f"✓ 加载词汇表成功，类别数: {num_classes}")
    
    # 创建模型
    print("\n=== 创建模型 ===")
    try:
        model = SLRModel(
            num_classes=num_classes,
            c2d_type='resnet18',
            conv_type=2,
            use_bn=1,
            gloss_dict=gloss_dict,
            attention_params=attention_params
        )
        print("✓ 模型创建成功")
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        return False
    
    # 检查注意力机制
    has_attention = check_attention_in_model(model)
    if not has_attention:
        return False
    
    # 测试前向传播
    forward_success = test_attention_forward(model)
    
    return forward_success

def compare_with_without_attention():
    """比较有无注意力机制的模型"""
    print("\n=== 比较有无注意力机制的模型 ===")
    
    # 加载词汇表
    dict_path = './preprocess/phoenix2014-T/gloss_dict.npy'
    if not os.path.exists(dict_path):
        print(f"✗ 词汇表文件不存在: {dict_path}")
        return False
    
    gloss_dict = np.load(dict_path, allow_pickle=True).item()
    num_classes = len(gloss_dict) + 1
    
    # 创建无注意力机制的模型
    print("创建无注意力机制的模型...")
    try:
        model_no_attention = SLRModel(
            num_classes=num_classes,
            c2d_type='resnet18',
            conv_type=2,
            use_bn=1,
            gloss_dict=gloss_dict
        )
        print("✓ 无注意力模型创建成功")
    except Exception as e:
        print(f"✗ 无注意力模型创建失败: {e}")
        return False
    
    # 创建有注意力机制的模型
    print("创建有注意力机制的模型...")
    attention_params = {
        'max_window_size': 7,
        'kernel_sizes': [3, 7, 11],
        'reduction_ratio': 8
    }
    
    try:
        model_with_attention = SLRModel(
            num_classes=num_classes,
            c2d_type='resnet18',
            conv_type=2,
            use_bn=1,
            gloss_dict=gloss_dict,
            attention_params=attention_params
        )
        print("✓ 有注意力模型创建成功")
    except Exception as e:
        print(f"✗ 有注意力模型创建失败: {e}")
        return False
    
    # 比较参数数量
    params_no_attention = sum(p.numel() for p in model_no_attention.parameters())
    params_with_attention = sum(p.numel() for p in model_with_attention.parameters())
    
    print(f"\n参数数量比较:")
    print(f"  无注意力模型: {params_no_attention:,} 参数")
    print(f"  有注意力模型: {params_with_attention:,} 参数")
    print(f"  差异: {params_with_attention - params_no_attention:,} 参数")
    
    if params_with_attention > params_no_attention:
        print("✓ 有注意力模型参数更多，说明注意力机制被正确添加")
    else:
        print("✗ 参数数量没有增加，可能注意力机制未被正确添加")
        return False
    
    return True

def main():
    print("动态时空注意力机制验证工具")
    print("=" * 50)
    
    # 测试1: 加载配置并测试
    success1 = load_config_and_test()
    
    # 测试2: 比较有无注意力机制的模型
    success2 = compare_with_without_attention()
    
    print("\n" + "=" * 50)
    print("验证结果总结:")
    print(f"配置加载和前向传播测试: {'✓ 通过' if success1 else '✗ 失败'}")
    print(f"模型比较测试: {'✓ 通过' if success2 else '✗ 失败'}")
    
    if success1 and success2:
        print("\n🎉 验证成功！您的代码确实使用了动态时空注意力机制。")
    else:
        print("\n❌ 验证失败！请检查注意力机制的实现和配置。")

if __name__ == '__main__':
    main()
