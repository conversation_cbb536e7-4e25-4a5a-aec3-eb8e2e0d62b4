#!/usr/bin/env python3
"""
Visualization tool for comparing CAM results across different layers or videos
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import argparse
from pathlib import Path


def load_cam_images(cam_dir, frame_indices=None, max_frames=10):
    """Load CAM images from directory"""
    cam_files = sorted([f for f in os.listdir(cam_dir) if f.endswith('_cam.jpg')])
    
    if frame_indices is None:
        # Select evenly spaced frames
        if len(cam_files) > max_frames:
            indices = np.linspace(0, len(cam_files)-1, max_frames, dtype=int)
            cam_files = [cam_files[i] for i in indices]
    else:
        cam_files = [f"frame_{i:04d}_cam.jpg" for i in frame_indices if f"frame_{i:04d}_cam.jpg" in cam_files]
    
    images = []
    for cam_file in cam_files:
        img_path = os.path.join(cam_dir, cam_file)
        if os.path.exists(img_path):
            img = cv2.imread(img_path)
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            images.append(img)
    
    return images, cam_files


def create_layer_comparison(video_dir, output_path, frame_indices=None, max_frames=8):
    """Create comparison visualization across different layers for one video"""
    # Find all layer directories
    layer_dirs = [d for d in os.listdir(video_dir) if os.path.isdir(os.path.join(video_dir, d))]
    layer_dirs = sorted(layer_dirs)
    
    if not layer_dirs:
        print(f"No layer directories found in {video_dir}")
        return
    
    print(f"Found layers: {layer_dirs}")
    
    # Load images for each layer
    all_images = {}
    all_files = {}
    
    for layer_dir in layer_dirs:
        layer_path = os.path.join(video_dir, layer_dir)
        video_name = os.listdir(layer_path)[0]  # Assume single video per layer
        video_path = os.path.join(layer_path, video_name)
        
        images, files = load_cam_images(video_path, frame_indices, max_frames)
        all_images[layer_dir] = images
        all_files[layer_dir] = files
    
    # Create comparison plot
    n_layers = len(layer_dirs)
    n_frames = len(all_images[layer_dirs[0]]) if layer_dirs else 0
    
    if n_frames == 0:
        print("No frames to display")
        return
    
    fig, axes = plt.subplots(n_layers, n_frames, figsize=(n_frames * 3, n_layers * 3))
    
    if n_layers == 1:
        axes = axes.reshape(1, -1)
    if n_frames == 1:
        axes = axes.reshape(-1, 1)
    
    for i, layer_dir in enumerate(layer_dirs):
        for j, img in enumerate(all_images[layer_dir]):
            if n_layers == 1 and n_frames == 1:
                ax = axes
            elif n_layers == 1:
                ax = axes[j]
            elif n_frames == 1:
                ax = axes[i]
            else:
                ax = axes[i, j]
            
            ax.imshow(img)
            ax.set_title(f"{layer_dir}\n{all_files[layer_dir][j]}", fontsize=8)
            ax.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"Layer comparison saved to: {output_path}")


def create_video_comparison(video_dirs, output_path, layer_name=None, frame_indices=None, max_frames=6):
    """Create comparison visualization across different videos for one layer"""
    if layer_name is None:
        # Use the first layer found
        first_video_dir = video_dirs[0]
        layer_dirs = [d for d in os.listdir(first_video_dir) if os.path.isdir(os.path.join(first_video_dir, d))]
        layer_name = layer_dirs[0] if layer_dirs else None
    
    if layer_name is None:
        print("No layer found for comparison")
        return
    
    print(f"Comparing videos for layer: {layer_name}")
    
    # Load images for each video
    all_images = {}
    all_files = {}
    
    for video_dir in video_dirs:
        video_name = os.path.basename(video_dir)
        layer_path = os.path.join(video_dir, layer_name)
        
        if not os.path.exists(layer_path):
            print(f"Layer {layer_name} not found in {video_dir}")
            continue
        
        # Find video subdirectory
        video_subdirs = [d for d in os.listdir(layer_path) if os.path.isdir(os.path.join(layer_path, d))]
        if not video_subdirs:
            continue
        
        video_subdir = video_subdirs[0]
        video_path = os.path.join(layer_path, video_subdir)
        
        images, files = load_cam_images(video_path, frame_indices, max_frames)
        all_images[video_name] = images
        all_files[video_name] = files
    
    # Create comparison plot
    video_names = list(all_images.keys())
    n_videos = len(video_names)
    n_frames = max(len(imgs) for imgs in all_images.values()) if all_images else 0
    
    if n_frames == 0:
        print("No frames to display")
        return
    
    fig, axes = plt.subplots(n_videos, n_frames, figsize=(n_frames * 3, n_videos * 3))
    
    if n_videos == 1:
        axes = axes.reshape(1, -1)
    if n_frames == 1:
        axes = axes.reshape(-1, 1)
    
    for i, video_name in enumerate(video_names):
        images = all_images[video_name]
        files = all_files[video_name]
        
        for j in range(n_frames):
            if n_videos == 1 and n_frames == 1:
                ax = axes
            elif n_videos == 1:
                ax = axes[j]
            elif n_frames == 1:
                ax = axes[i]
            else:
                ax = axes[i, j]
            
            if j < len(images):
                ax.imshow(images[j])
                ax.set_title(f"{video_name}\n{files[j]}", fontsize=8)
            else:
                ax.set_title(f"{video_name}\n(no frame)", fontsize=8)
            
            ax.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"Video comparison saved to: {output_path}")


def main():
    parser = argparse.ArgumentParser(description='Visualize and compare CAM results')
    parser.add_argument('--input_dir', type=str, required=True,
                        help='Input directory containing CAM results')
    parser.add_argument('--output_dir', type=str, default='./cam_comparisons',
                        help='Output directory for comparison visualizations')
    parser.add_argument('--comparison_type', type=str, choices=['layers', 'videos'], default='layers',
                        help='Type of comparison to create')
    parser.add_argument('--video_ids', type=int, nargs='+', default=None,
                        help='Specific video IDs to compare (for video comparison)')
    parser.add_argument('--layer_name', type=str, default=None,
                        help='Specific layer name for video comparison')
    parser.add_argument('--frame_indices', type=int, nargs='+', default=None,
                        help='Specific frame indices to include')
    parser.add_argument('--max_frames', type=int, default=8,
                        help='Maximum number of frames to include')
    
    args = parser.parse_args()
    
    os.makedirs(args.output_dir, exist_ok=True)
    
    if args.comparison_type == 'layers':
        # Compare layers for each video
        video_dirs = [d for d in Path(args.input_dir).iterdir() if d.is_dir()]
        
        for video_dir in video_dirs:
            video_name = video_dir.name
            output_path = os.path.join(args.output_dir, f"{video_name}_layer_comparison.png")
            create_layer_comparison(str(video_dir), output_path, args.frame_indices, args.max_frames)
    
    elif args.comparison_type == 'videos':
        # Compare videos for specific layer
        if args.video_ids:
            video_dirs = [os.path.join(args.input_dir, f"video_{vid_id:04d}") for vid_id in args.video_ids]
            video_dirs = [d for d in video_dirs if os.path.exists(d)]
        else:
            video_dirs = [str(d) for d in Path(args.input_dir).iterdir() if d.is_dir()]
        
        output_path = os.path.join(args.output_dir, f"video_comparison_{args.layer_name or 'default'}.png")
        create_video_comparison(video_dirs, output_path, args.layer_name, args.frame_indices, args.max_frames)
    
    print("Comparison visualization completed!")


if __name__ == "__main__":
    main()
