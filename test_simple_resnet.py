#!/usr/bin/env python3
"""
Simple test to check ResNet class definition
"""

import sys
import os
import importlib

# Clear any cached modules
if 'modules.resnet' in sys.modules:
    del sys.modules['modules.resnet']
if 'modules.attention' in sys.modules:
    del sys.modules['modules.attention']

# Force reload
try:
    from modules import resnet
    importlib.reload(resnet)
    
    from modules import attention
    importlib.reload(attention)
    
    print("Modules reloaded successfully")
    
    # Check ResNet class
    import inspect
    sig = inspect.signature(resnet.ResNet.__init__)
    print(f"ResNet.__init__ signature: {sig}")
    
    # Check if attention_params is in the signature
    params = list(sig.parameters.keys())
    print(f"Parameters: {params}")
    
    if 'attention_params' in params:
        print("✓ attention_params found in ResNet.__init__")
        
        # Try to create ResNet with attention_params
        attention_params = {
            'max_window_size': 9,
            'kernel_sizes': [3, 5, 7],
            'reduction_ratio': 16
        }
        
        model = resnet.ResNet(resnet.BasicBlock, [2, 2, 2, 2], attention_params=attention_params)
        print("✓ ResNet created successfully with attention_params")
        
        # Check if DTA modules exist
        if hasattr(model, 'corr1') and hasattr(model, 'corr2'):
            print("✓ DynamicTemporalAttention modules found")
        else:
            print("✗ DynamicTemporalAttention modules not found")
            
    else:
        print("✗ attention_params NOT found in ResNet.__init__")
        print("This suggests there might be a different ResNet class being imported")
        
        # Check where ResNet is coming from
        print(f"ResNet class location: {resnet.ResNet.__module__}")
        print(f"ResNet file: {inspect.getfile(resnet.ResNet)}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
