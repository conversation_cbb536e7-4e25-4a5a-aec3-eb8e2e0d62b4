#!/usr/bin/env python3
"""
Debug script to test ResNet initialization
"""

import sys
import os

def test_resnet_import():
    """Test ResNet import and initialization"""
    print("Testing ResNet import and initialization...")
    
    try:
        # Import modules
        from modules import resnet
        from modules.attention import DynamicTemporalAttention
        print("✓ Modules imported successfully")
        
        # Check resnet18 function signature
        import inspect
        sig = inspect.signature(resnet.resnet18)
        print(f"✓ resnet18 signature: {sig}")
        
        # Check ResNet class signature
        sig_resnet = inspect.signature(resnet.ResNet.__init__)
        print(f"✓ ResNet.__init__ signature: {sig_resnet}")
        
        # Test ResNet class creation directly
        print("\nTesting ResNet class creation...")
        from modules.resnet import ResNet, BasicBlock
        
        # Test without attention_params
        model1 = ResNet(BasicBlock, [2, 2, 2, 2])
        print("✓ ResNet created without attention_params")
        
        # Test with attention_params
        attention_params = {
            'max_window_size': 9,
            'kernel_sizes': [3, 5, 7],
            'reduction_ratio': 16
        }
        model2 = ResNet(BasicBlock, [2, 2, 2, 2], attention_params=attention_params)
        print("✓ ResNet created with attention_params")
        
        # Test resnet18 function
        print("\nTesting resnet18 function...")
        model3 = resnet.resnet18()
        print("✓ resnet18() created without params")
        
        model4 = resnet.resnet18(attention_params=attention_params)
        print("✓ resnet18() created with attention_params")
        
        # Check if DTA modules exist
        if hasattr(model4, 'corr1') and hasattr(model4, 'corr2'):
            print("✓ DynamicTemporalAttention modules found")
            
            # Check target layers
            target_layers = ['corr2.fusion.0', 'corr1.fusion.0']
            for layer_name in target_layers:
                layer = model4
                try:
                    for attr in layer_name.split('.'):
                        layer = getattr(layer, attr)
                    print(f"✓ Layer {layer_name} found")
                except AttributeError:
                    print(f"✗ Layer {layer_name} not found")
        else:
            print("✗ DynamicTemporalAttention modules not found")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_slr_model():
    """Test SLRModel creation"""
    print("\n" + "="*50)
    print("Testing SLRModel creation...")
    
    try:
        from slr_network import SLRModel
        
        # Test without attention_params
        print("Creating SLRModel without attention_params...")
        model1 = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)
        print("✓ SLRModel created without attention_params")
        
        # Test with attention_params
        print("Creating SLRModel with attention_params...")
        attention_params = {
            'max_window_size': 9,
            'kernel_sizes': [3, 5, 7],
            'reduction_ratio': 16
        }
        model2 = SLRModel(
            num_classes=1000, 
            c2d_type='resnet18', 
            conv_type=2, 
            use_bn=1,
            attention_params=attention_params
        )
        print("✓ SLRModel created with attention_params")
        
        # Check conv2d module
        if hasattr(model2.conv2d, 'corr1') and hasattr(model2.conv2d, 'corr2'):
            print("✓ DynamicTemporalAttention modules found in SLRModel")
        else:
            print("✗ DynamicTemporalAttention modules not found in SLRModel")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_target_layers():
    """Test target layer access"""
    print("\n" + "="*50)
    print("Testing target layer access...")
    
    try:
        from slr_network import SLRModel
        
        model = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)
        
        # Test target layers
        target_layers = [
            'conv2d.corr2.fusion.0',
            'conv2d.corr2.temporal_gate.0',
            'conv2d.corr1.fusion.0',
            'conv2d.corr1.temporal_gate.0',
            'conv2d.layer4.1.conv2',
            'conv2d.layer3.1.conv2'
        ]
        
        found_layers = []
        missing_layers = []
        
        for layer_name in target_layers:
            layer = model
            try:
                for attr in layer_name.split('.'):
                    layer = getattr(layer, attr)
                print(f"✓ Layer {layer_name} found")
                found_layers.append(layer_name)
            except AttributeError:
                print(f"✗ Layer {layer_name} not found")
                missing_layers.append(layer_name)
        
        print(f"\nSummary: {len(found_layers)} found, {len(missing_layers)} missing")
        
        if missing_layers:
            print("Missing layers:")
            for layer in missing_layers:
                print(f"  - {layer}")
        
        return len(found_layers) > 0
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    print("=== ResNet Debug Script ===\n")
    
    tests = [
        ("ResNet Import and Initialization", test_resnet_import),
        ("SLRModel Creation", test_slr_model),
        ("Target Layer Access", test_target_layers)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"Running: {test_name}")
        print("-" * 50)
        
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("DEBUG SUMMARY")
    print('='*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status:<8} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! ResNet setup is working correctly.")
    else:
        print(f"\n⚠ {total-passed} tests failed. Check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
