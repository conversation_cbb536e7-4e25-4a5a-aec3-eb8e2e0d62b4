#!/usr/bin/env python3
"""
Quick start script for DynamicTemporalAttention CAM generation
"""

import os
import argparse
import sys
import subprocess
from pathlib import Path

def find_checkpoint():
    """Find available model checkpoints"""
    checkpoint_paths = [
        './work_dir/baseline_res18_attention/_best_model.pt',
        './work_dir/baseline_res18_attention/best_model.pt',
        './pretrain_model/dev_18.90_PHOENIX14-T.pt'
    ]
    
    # Search in work_dir subdirectories
    work_dir = Path('./work_dir')
    if work_dir.exists():
        for subdir in work_dir.iterdir():
            if subdir.is_dir():
                for checkpoint_file in ['_best_model.pt', 'best_model.pt', 'model.pt']:
                    checkpoint_path = subdir / checkpoint_file
                    if checkpoint_path.exists():
                        checkpoint_paths.append(str(checkpoint_path))
    
    # Return first existing checkpoint
    for path in checkpoint_paths:
        if os.path.exists(path):
            return path
    
    return None

def find_dataset():
    """Find available dataset"""
    dataset_paths = [
        './dataset/phoenix2014/phoenix-2014-multisigner',
        './dataset/phoenix2014',
        '../dataset/phoenix2014/phoenix-2014-multisigner'
    ]
    
    for path in dataset_paths:
        if os.path.exists(path):
            return path
    
    return None

def find_dict_path():
    """Find gloss dictionary"""
    dict_paths = [
        './preprocess/phoenix2014/gloss_dict.npy',
        './preprocess/gloss_dict.npy',
        '../preprocess/phoenix2014/gloss_dict.npy'
    ]
    
    for path in dict_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_dta_requirements():
    """Check if DTA requirements are met"""
    print("Checking DynamicTemporalAttention requirements...")
    
    # Check imports
    try:
        import torch
        import cv2
        import numpy as np
        from slr_network import SLRModel
        from utils.device import GpuDataParallel
        from modules.attention import DynamicTemporalAttention
        from generate_cam_dta import DynamicTemporalAttentionCAM
        print("✓ All required modules available")
    except ImportError as e:
        print(f"✗ Missing required module: {e}")
        return False
    
    # Check CUDA
    if torch.cuda.is_available():
        print(f"✓ CUDA available: {torch.cuda.get_device_name(0)}")
    else:
        print("⚠ CUDA not available, using CPU")
    
    # Test DTA model creation
    try:
        model = SLRModel(num_classes=1000, c2d_type='resnet18', conv_type=2, use_bn=1)
        if hasattr(model.conv2d, 'corr1') and hasattr(model.conv2d, 'corr2'):
            print("✓ DynamicTemporalAttention modules found")
        else:
            print("✗ DynamicTemporalAttention modules not found")
            return False
    except Exception as e:
        print(f"✗ DTA model creation failed: {e}")
        return False
    
    return True

def setup_paths():
    """Automatically find and setup paths"""
    print("Setting up paths...")
    
    checkpoint = find_checkpoint()
    if checkpoint:
        print(f"✓ Found checkpoint: {checkpoint}")
    else:
        print("✗ No checkpoint found")
        print("Please place a model checkpoint in one of these locations:")
        print("  - ./work_dir/baseline_res18_attention/_best_model.pt")
        print("  - ./pretrain_model/dev_18.90_PHOENIX14-T.pt")
        return None, None, None
    
    dataset_prefix = find_dataset()
    if dataset_prefix:
        print(f"✓ Found dataset: {dataset_prefix}")
    else:
        print("✗ No dataset found")
        print("Please set up dataset in: ./dataset/phoenix2014/phoenix-2014-multisigner")
        return checkpoint, None, None
    
    dict_path = find_dict_path()
    if dict_path:
        print(f"✓ Found dictionary: {dict_path}")
    else:
        print("✗ No gloss dictionary found")
        print("Please set up dictionary in: ./preprocess/phoenix2014/gloss_dict.npy")
        return checkpoint, dataset_prefix, None
    
    return checkpoint, dataset_prefix, dict_path

def run_dta_cam(checkpoint, dataset_prefix, dict_path, video_id=0, gpu_id=0, 
                target_layers=None, visualize_attention=True):
    """Run DynamicTemporalAttention CAM generation"""
    print(f"\nGenerating DTA CAM for video {video_id}...")
    
    if target_layers is None:
        target_layers = ['conv2d.corr2.fusion.0', 'conv2d.corr2.temporal_gate.0']
    
    cmd = [
        sys.executable, 'generate_cam_dta.py',
        '--checkpoint', checkpoint,
        '--dataset', 'phoenix2014',
        '--prefix', dataset_prefix,
        '--dict_path', dict_path,
        '--select_id', str(video_id),
        '--gpu_id', str(gpu_id),
        '--output_dir', './quick_DTA_CAM_results',
        '--target_layers'] + target_layers + [
        '--alpha', '0.4',
        '--save_original'
    ]
    
    if visualize_attention:
        cmd.append('--visualize_attention')
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ DTA CAM generation completed successfully!")
        print(f"Output saved to: ./quick_DTA_CAM_results")
        
        # Check for attention weights
        attention_dir = './quick_DTA_CAM_results'
        for layer in target_layers:
            layer_dir = os.path.join(attention_dir, layer.replace('.', '_'))
            if os.path.exists(layer_dir):
                attention_weights_dir = os.path.join(layer_dir, 'attention_weights')
                if os.path.exists(attention_weights_dir):
                    print(f"✓ Attention weights saved to: {attention_weights_dir}")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ DTA CAM generation failed:")
        print(f"Error: {e.stderr}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Quick start DynamicTemporalAttention CAM generation')
    parser.add_argument('--video_id', type=int, default=0,
                        help='Video ID to process (default: 0)')
    parser.add_argument('--gpu_id', type=int, default=0,
                        help='GPU ID to use (default: 0)')
    parser.add_argument('--test_only', action='store_true',
                        help='Only test setup, do not generate CAM')
    parser.add_argument('--target_layers', type=str, nargs='+',
                        default=['conv2d.corr2.fusion.0', 'conv2d.corr2.temporal_gate.0'],
                        help='Target layers for CAM generation')
    parser.add_argument('--no_attention', action='store_true',
                        help='Skip attention weight visualization')
    
    args = parser.parse_args()
    
    print("=== Quick Start DynamicTemporalAttention CAM Generation ===\n")
    
    # Check DTA requirements
    if not check_dta_requirements():
        print("\n❌ DTA requirements check failed. Please install missing dependencies.")
        return False
    
    # Setup paths
    checkpoint, dataset_prefix, dict_path = setup_paths()
    
    if not all([checkpoint, dataset_prefix, dict_path]):
        print("\n❌ Path setup failed. Please check the missing files above.")
        return False
    
    if args.test_only:
        print("\n✅ DTA test completed successfully! All paths are set up correctly.")
        print("\nTo generate DTA CAM, run:")
        print(f"python quick_start_dta.py --video_id {args.video_id}")
        print("\nDTA-specific features:")
        print("- Multi-scale temporal attention visualization")
        print("- Temporal gate activation maps")
        print("- Feature fusion analysis")
        print("- Attention weight plots")
        return True
    
    # Run DTA CAM generation
    success = run_dta_cam(
        checkpoint, dataset_prefix, dict_path, 
        args.video_id, args.gpu_id, args.target_layers,
        visualize_attention=not args.no_attention
    )
    
    if success:
        print("\n🎉 DynamicTemporalAttention CAM generation completed successfully!")
        print("\nGenerated outputs:")
        print("1. CAM overlays: ./quick_DTA_CAM_results/*/frame_*_cam.jpg")
        print("2. Heatmaps: ./quick_DTA_CAM_results/*/frame_*_heatmap.jpg")
        if not args.no_attention:
            print("3. Attention weights: ./quick_DTA_CAM_results/*/attention_weights/")
        
        print("\nNext steps:")
        print("1. Check the results in ./quick_DTA_CAM_results")
        print("2. Try different layers: python quick_start_dta.py --target_layers conv2d.corr1.fusion.0")
        print("3. Try different videos: python quick_start_dta.py --video_id 1")
        print("4. Batch processing: python batch_generate_cam_dta.py --help")
        print("5. Advanced features: python generate_cam_dta.py --help")
        
        print("\nDTA Analysis Tips:")
        print("- Compare fusion.0 vs temporal_gate.0 layers")
        print("- Look for temporal patterns in attention weights")
        print("- Check window_weights.png for adaptive temporal modeling")
        print("- Analyze gate_weights.png for temporal selection")
    else:
        print("\n❌ DTA CAM generation failed. Please check the error messages above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
