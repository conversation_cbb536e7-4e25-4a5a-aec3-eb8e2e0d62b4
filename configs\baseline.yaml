feeder: dataset.dataloader_video.BaseFeeder
phase: train
dataset: phoenix2014
# dataset: phoenix14-si5
num_epoch: 40
work_dir: ./work_dir/baseline_res18_attention_7_3711_8/
batch_size: 2
random_seed: 0
test_batch_size: 2
num_worker: 10
device: 0
log_interval: 10000
eval_interval: 1
save_interval: 5
# python in default
evaluate_tool: python #sclite
loss_weights:
  SeqCTC: 1.0
  # VAC
  ConvCTC: 1.0
  Dist: 25.0
#load_weights: ''

optimizer_args:
  optimizer: Adam
  base_lr: 0.0001
  step: [ 20, 35]
  learning_ratio: 1
  weight_decay: 0.0001
  start_epoch: 0
  nesterov: False

feeder_args:
  mode: 'train'
  datatype: 'video'
  num_gloss: -1
  drop_ratio: 1.0
  frame_interval: 1
  image_scale: 1.0  # 0-1 represents ratio, >1 represents absolute value
  input_size: 224

model: slr_network.SLRModel
decode_mode: beam
model_args:
  num_classes: 1296
  c2d_type: resnet18 #resnet18, mobilenet_v2, squeezenet1_1, shufflenet_v2_x1_0, efficientnet_b1, mnasnet1_0, regnet_y_800mf, vgg16_bn, vgg11_bn, regnet_x_800mf, regnet_x_400mf, densenet121, regnet_y_1_6gf
  conv_type: 2
  use_bn: 1
  # SMKD
  share_classifier: True
  weight_norm: True

# 动态时间注意力机制参数
# attention_params:
#   max_window_size: 11
#   kernel_sizes: [5, 9, 13]
#   reduction_ratio: 16
attention_params:
  max_window_size: 7
  kernel_sizes: [3, 7, 11]
  reduction_ratio: 8